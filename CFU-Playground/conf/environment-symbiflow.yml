name: cfu-symbiflow
channels:
  - defaults
  - litex-hub
dependencies:
  - litex-hub::gcc-riscv32-elf-newlib
  - litex-hub::openfpgaloader
  - litex-hub::dfu-util
  - litex-hub::flterm
  - litex-hub::openocd
  - litex-hub::verilator
  - litex-hub::nextpnr-nexus
  - litex-hub::nextpnr-ecp5
  - litex-hub::nextpnr-ice40
  - litex-hub::yosys
  - litex-hub::iceprog
  - litex-hub::prjxray-tools
  - litex-hub::prjxray-db
  - litex-hub::vtr-optimized
  - litex-hub::symbiflow-yosys-plugins
  - lxml
  - simplejson
  - intervaltree
  - json-c
  - libevent
  - python=3.7
  - pip
  - pip:
    - -r ./requirements-symbiflow.txt
