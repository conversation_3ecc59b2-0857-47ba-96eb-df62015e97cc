#!/usr/bin/expect -f
#
# Choose Verilator simulation mode by making the first arg "s" (no dash)
#  or Renode simulation mode by making the first arg "r" (no dash).
#  Then no other options, just the menu choices.
#
# For non-simulation (Arty) mode:
#  first arg:  path to binary (kernel)
#  second arg: path to tty device (e.g. /dev/ttyUSB1)
#  third arg: UART SPEED
#  subsequent args: main menu choices
#

set timeout 500
set simpid 0



if { [lindex $argv 0] == "s" } {
  set choices [lrange $argv 1 end]
  set simpid [spawn make PLATFORM=sim load]
  send_user "Sim PID $simpid\n"
} elseif { [lindex $argv 0] == "r" } {
  set choices [lrange $argv 1 end]
  set simpid [spawn make TARGET=$env(TARGET) renode-headless]
  send_user "Sim PID $simpid\n"
  send "\n"
} else {
  spawn ../../soc/bin/litex_term --speed [lindex $argv 2] --kernel [lindex $argv 0] [lindex $argv 1]
  set choices [lrange $argv 3 end]
}

set timeout 5
expect {
   "CFU Playground"   {send_user "\n(At top level menu)\n"}
   timeout            {send_user "\nLooking for top-level menu...\n"; send "x"; exp_continue}
}

set timeout 500

foreach c $choices {

  expect "===="
  expect ">"
  send_user "Sending $c...\n"
  send $c
# sleep 1

}

expect "===="
expect ">"

send_user "\nFinished interaction\n"

send_user "\nDisconnecting...\n"

if { $simpid > 0 } {
  send_user "\nKilling process $simpid\n"
  exec kill $simpid
}

close
