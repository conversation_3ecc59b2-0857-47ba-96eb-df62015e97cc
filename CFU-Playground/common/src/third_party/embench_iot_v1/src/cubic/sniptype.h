/* BEEBS cubic benchmark

   This version, copyright (C) 2013-2019 Embecosm Limited and University of
   Bristol

   Contributor: <PERSON> <<EMAIL>>
   Contributor <PERSON> <<EMAIL>>

   This file is part of Embench and was formerly part of the Bristol/Embecosm
   Embedded Benchmark Suite.

   SPDX-License-Identifier: GPL-3.0-or-later

   The original code is from http://www.snippets.org/. */

/* +++Date last modified: 05-Jul-1997 */

/*
 **  SNIPTYPE.H - Include file for SNIPPETS data types and commonly used macros
 */

#ifndef SNIPTYPE__H
#define SNIPTYPE__H

typedef unsigned char BYTE;
typedef unsigned long DWORD;
typedef unsigned short WORD;

#endif /* SNIPTYPE__H */

/* vim: set ts=3 sw=3 et: */


/*
   Local Variables:
   mode: C
   c-file-style: "gnu"
   End:
*/
