name: oxide_fmax_conda
on:
  workflow_dispatch:
  schedule:
    - cron: "23 12 * * *"
jobs:
  oxide-build-and-runs-conda:
    runs-on: ubuntu-latest
    steps:
      - run: wget --progress=dot:giga -O- https://static.dev.sifive.com/dev-tools/riscv64-unknown-elf-gcc-8.3.0-2020.04.1-x86_64-linux-ubuntu14.tar.gz | tar -xzC /opt
      - run: echo "/opt/riscv64-unknown-elf-gcc-8.3.0-2020.04.1-x86_64-linux-ubuntu14/bin" >> $GITHUB_PATH
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v3
        with:
          python-version: '3.7'
      - run: bash scripts/setup -ci
      - run: which pip3 && which python3 && which pip
      - run: riscv64-unknown-elf-gcc --version
      - run: make env
      - run: pwd && source env/conda/bin/activate cfu-common && which yosys && yosys --version && which nextpnr-nexus && nextpnr-nexus --version
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/proj_template_v && ulimit -S -t 900  && ulimit -H -t 900  && make PLATFORM=hps EXTRA_LITEX_ARGS="--cpu-variant=full     --nextpnr-seed=1"   clean bitstream |& egrep '(Max frequency|Info:.*%)' || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/proj_template_v && ulimit -S -t 900  && ulimit -H -t 900  && make PLATFORM=hps EXTRA_LITEX_ARGS="--cpu-variant=full     --nextpnr-seed=22"  clean bitstream |& egrep '(Max frequency|Info:.*%)' || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/proj_template_v && ulimit -S -t 900  && ulimit -H -t 900  && make PLATFORM=hps EXTRA_LITEX_ARGS="--cpu-variant=full     --nextpnr-seed=333" clean bitstream |& egrep '(Max frequency|Info:.*%)' || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/proj_template_v && ulimit -S -t 900  && ulimit -H -t 900  && make PLATFORM=hps EXTRA_LITEX_ARGS="--cpu-variant=slim+cfu --nextpnr-seed=1"   clean bitstream |& egrep '(Max frequency|Info:.*%)' || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/proj_template_v && ulimit -S -t 900  && ulimit -H -t 900  && make PLATFORM=hps EXTRA_LITEX_ARGS="--cpu-variant=slim+cfu --nextpnr-seed=22"  clean bitstream |& egrep '(Max frequency|Info:.*%)' || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/proj_template_v && ulimit -S -t 900  && ulimit -H -t 900  && make PLATFORM=hps EXTRA_LITEX_ARGS="--cpu-variant=slim+cfu --nextpnr-seed=333" clean bitstream |& egrep '(Max frequency|Info:.*%)' || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/hps_accel       && ulimit -S -t 1200  && ulimit -H -t 1200  && make PLATFORM=hps EXTRA_LITEX_ARGS="--cpu-variant=slim+cfu --nextpnr-seed=1"   clean bitstream |& egrep '(Max frequency|Info:.*%)' || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/hps_accel       && ulimit -S -t 1200  && ulimit -H -t 1200  && make PLATFORM=hps EXTRA_LITEX_ARGS="--cpu-variant=slim+cfu --nextpnr-seed=22"  clean bitstream |& egrep '(Max frequency|Info:.*%)' || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/hps_accel       && ulimit -S -t 1200  && ulimit -H -t 1200  && make PLATFORM=hps EXTRA_LITEX_ARGS="--cpu-variant=slim+cfu --nextpnr-seed=333" clean bitstream |& egrep '(Max frequency|Info:.*%)' || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/hps_accel       && ulimit -S -t 1200  && ulimit -H -t 1200  && make PLATFORM=hps EXTRA_LITEX_ARGS="--cpu-variant=slimopt+cfu --nextpnr-seed=1"   clean bitstream |& egrep '(Max frequency|Info:.*%)' || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/hps_accel       && ulimit -S -t 1200  && ulimit -H -t 1200  && make PLATFORM=hps EXTRA_LITEX_ARGS="--cpu-variant=slimopt+cfu --nextpnr-seed=22"  clean bitstream |& egrep '(Max frequency|Info:.*%)' || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/hps_accel       && ulimit -S -t 1200  && ulimit -H -t 1200  && make PLATFORM=hps EXTRA_LITEX_ARGS="--cpu-variant=slimopt+cfu --nextpnr-seed=333" clean bitstream |& egrep '(Max frequency|Info:.*%)' || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/hps_accel       && ulimit -S -t 1200  && ulimit -H -t 1200  && make PLATFORM=hps EXTRA_LITEX_ARGS="--separate-arena --cfu-mport --cpu-variant=slimopt+cfu --nextpnr-seed=1"   clean bitstream |& egrep '(Max frequency|Info:.*%)' || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/hps_accel       && ulimit -S -t 1200  && ulimit -H -t 1200  && make PLATFORM=hps EXTRA_LITEX_ARGS="--separate-arena --cfu-mport --cpu-variant=slimopt+cfu --nextpnr-seed=22"  clean bitstream |& egrep '(Max frequency|Info:.*%)' || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/hps_accel       && ulimit -S -t 1200  && ulimit -H -t 1200  && make PLATFORM=hps EXTRA_LITEX_ARGS="--separate-arena --cfu-mport --cpu-variant=slimopt+cfu --nextpnr-seed=333" clean bitstream |& egrep '(Max frequency|Info:.*%)' || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/hps_accel       && ulimit -S -t 240  && ulimit -H -t 240  && make PLATFORM=hps EXTRA_NEXTPNR_ARGS="--placer-heap-timingweight 52" EXTRA_LITEX_ARGS="--separate-arena --cfu-mport --cpu-variant=slimopt+cfu --extra-nextpnr-params"   clean bitstream |& egrep '(Max frequency|Info:.*%)' || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/hps_accel       && ulimit -S -t 240  && ulimit -H -t 240  && make PLATFORM=hps EXTRA_NEXTPNR_ARGS="--placer-heap-timingweight 52" EXTRA_LITEX_ARGS="--separate-arena --cfu-mport --cpu-variant=slimopt+cfu --extra-nextpnr-params --no-compile-software"   clean bitstream |& egrep '(Max frequency|Info:.*%)' || true
