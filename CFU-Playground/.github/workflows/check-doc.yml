name: check-doc
on:
  push:
    paths:
      - 'docs/**'
  pull_request:
    paths:
      - 'docs/**'

jobs:
  check-docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v3
        with:
          python-version: '3.7'
      - run: pwd && cd docs && pwd 
      - run: pwd && cd .. && pwd 
      - run: pwd && cd docs && pip3 install -r requirements.txt && make html && ls -R
