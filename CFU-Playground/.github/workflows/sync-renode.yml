name: Update Renode

on:
  schedule:
    - cron: 0 2 * * *

  # Allow manual triggering of the workflow.
  workflow_dispatch: {}

jobs:
  sync-renode:
    runs-on: ubuntu-latest

    if: |
      github.repository_owner == 'google' && ( github.event_name == 'workflow_dispatch' || github.event_name == 'schedule' )

    env:
      GH_SERVICE_ACCOUNT_NAME: "CFU-Playground-Bot"
      GH_SERVICE_ACCOUNT_EMAIL: "<EMAIL>"

    steps:
      - name: Clone repository
        uses: actions/checkout@v2
        with:
          ssh-key: ${{ secrets.SSH_DEPLOY_KEY }}

      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.7'

      - name: Configure git
        run: |
          git config --global user.name $GH_SERVICE_ACCOUNT_NAME
          git config --global user.email $GH_SERVICE_ACCOUNT_EMAIL

      - name: Sync the code and commit
        id: sync-and-commit
        run: |
          rm -rf ./third_party/renode/*
          wget --progress=dot:giga "https://dl.antmicro.com/projects/renode/builds/renode-latest.linux-portable.tar.gz"
          tar xf renode-latest.linux-portable.tar.gz

          RENODE_DIRNAME=$(basename renode_*_portable)
          RENODE_VERSION=${RENODE_DIRNAME#*_}
          RENODE_VERSION=${RENODE_VERSION%_*}

          if [ "$RENODE_VERSION" != "$(cat conf/renode.version)" ]; then
            echo $RENODE_VERSION > conf/renode.version
            git add conf/renode.version

            RENODE_SHA=${RENODE_VERSION#*git}
            git commit -sm "Update Renode to $RENODE_SHA."
            echo "::set-output name=committed::true"
          else
            echo "::notice title=Early exit::Renode is up to date."
            echo "::set-output name=committed::false"
          fi

          rm renode-latest.linux-portable.tar.gz
          rm -rf renode_*_portable

      - name: Create Pull Request
        id: create-pr
        if: steps.sync-and-commit.outputs.committed == 'true'
        uses: peter-evans/create-pull-request@v3
        with:
          branch: update-renode
          base: main
          delete-branch: true
          signoff: true
          committer: CFU Playground Bot <<EMAIL>>
          author: CFU Playground Bot <<EMAIL>>
          token: ${{ secrets.CFU_BOT_TOKEN }}
          title: (CFU Playground) Automatic Renode update from builds.renode.io
          commit-message: Automatic Renode update from builds.renode.io
          body: "(CFU Playground) Automatic Renode update from builds.renode.io"

      - name: Enable automerge
        if: steps.create-pr.outputs.pull-request-operation == 'created'
        uses: peter-evans/enable-pull-request-automerge@v1
        with:
          token: ${{ secrets.CFU_BOT_TOKEN }}
          merge-method: rebase
          pull-request-number: ${{ steps.create-pr.outputs.pull-request-number }}
