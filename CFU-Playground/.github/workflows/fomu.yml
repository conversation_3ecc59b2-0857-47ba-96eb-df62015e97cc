name: fomu_ci
on: 
  pull_request:
    paths-ignore:
      - 'docs/**'
      - 'README.md'
      - '**/README.md'
      - 'LICENSE'
      - 'CONTRIBUTING.md'
      - 'CODE_OF_CONDUCT.md'
      - 'AUTHORS'
jobs:
  fomu-build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v3
        with:
          python-version: '3.7'
      - run: bash scripts/setup -ci
      - run: which pip3 && which python3 && which pip
      - run: make env
      - run: source env/conda/bin/activate cfu-common && which pip3 && which python3 && which pip
      - run: source env/conda/bin/activate cfu-common && riscv32-elf-gcc --version
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && yosys --version && nextpnr-ice40 --version
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/proj_template_v && pip3 list && make TARGET=kosagi_fomu bitstream
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/kws_micro_accel && pip3 list && make TARGET=kosagi_fomu bitstream || true
