name: Test projects
on:
  push:
    paths-ignore:
      - 'docs/**'
      - 'README.md'
      - '**/README.md'
      - 'LICENSE'
      - 'CONTRIBUTING.md'
      - 'CODE_OF_CONDUCT.md'
      - 'AUTHORS'
  pull_request:
    paths-ignore:
      - 'docs/**'
      - 'README.md'
      - '**/README.md'
      - 'LICENSE'
      - 'CONTRIBUTING.md'
      - 'CODE_OF_CONDUCT.md'
      - 'AUTHORS'
  workflow_dispatch:
    paths-ignore:
      - 'docs/**'
      - 'README.md'
      - '**/README.md'
      - 'LICENSE'
      - 'CONTRIBUTING.md'
      - 'CODE_OF_CONDUCT.md'
      - 'AUTHORS'
jobs:
  setup-matrix:
    runs-on: ubuntu-20.04
    outputs:
      matrix-combinations: ${{ steps.setup-matrix-combinations.outputs.matrix-combinations }}

    defaults:
      run:
        shell: bash -l {0}

    steps:
    - name: Clone repository
      uses: actions/checkout@v3

    - name: Setup Python
      uses: actions/setup-python@v3
      with:
        python-version: '3.7'

    - name: Setup matrix combinations
      id: setup-matrix-combinations
      run: |
        export MATRIX_PARAMS=$(./.github/scripts/generate_ci_matrix.py)
        echo "::set-output name=matrix-combinations::{\"include\":$MATRIX_PARAMS}"

    - name: Setup environment
      run: |
        echo "RENODE_VERSION=$(cat conf/renode.version)" >> $GITHUB_ENV
        echo "RENODE_DIR=third_party/renode" >> $GITHUB_ENV

    - name: Download Renode
      uses: antmicro/renode-test-action@v2.0.0
      with:
        renode-version: '${{ env.RENODE_VERSION }}'
        renode-path: '${{ env.RENODE_DIR }}'

    - name: Cache Renode installation
      uses: actions/cache@v3
      id: cache-renode
      with:
        path: '${{ env.RENODE_DIR }}'
        key: cfu-cache-renode-${{ env.RENODE_VERSION }}

    - name: Cache Conda packages
      uses: actions/cache@v3
      id: cache-conda
      with:
        path: ~/conda_pkgs_dir
        key: cfu-cache-conda-${{ github.sha }}

    - name: Build Conda Env
      uses: conda-incubator/setup-miniconda@v2
      with:
        auto-update-conda: true
        python-version: '3.7'
        environment-file: .github/misc/test-environment.yml
        activate-environment: "test"
        
    - name: check conda env
      run: |
        conda info
        conda list
        yosys --version


  test-projects:
    runs-on: ubuntu-20.04
    continue-on-error: True
    needs: setup-matrix
    strategy:
      matrix: ${{ fromJson(needs.setup-matrix.outputs.matrix-combinations) }}

    defaults:
      run:
        shell: bash -l {0}

    steps:
    - name: Clone repository
      uses: actions/checkout@v3
      with:
        submodules: true

    - name: Setup Python
      uses: actions/setup-python@v3
      with:
        python-version: '3.7'

    - name: Prepare Renode version
      run: |
        echo "RENODE_VERSION=$(cat conf/renode.version)" >> $GITHUB_ENV
        echo "RENODE_DIR=third_party/renode" >> $GITHUB_ENV

    - name: Restore Renode
      uses: actions/cache@v3
      id: cache-renode
      with:
        path: '${{ env.RENODE_DIR }}'
        key: cfu-cache-renode-${{ env.RENODE_VERSION }}

    - name: Restore cached Conda packages
      uses: actions/cache@v3
      id: cache-conda
      with:
        path: ~/conda_pkgs_dir
        key: cfu-cache-conda-${{ github.sha }}

    - name: Build Conda Env
      uses: conda-incubator/setup-miniconda@v2
      with:
        auto-update-conda: true
        python-version: '3.7'
        environment-file: .github/misc/test-environment.yml
        activate-environment: "test"

    - name: Setup environment
      run: |
        bash scripts/setup -ci

    - name: Build sample & generate Renode scripts
      run:
        pwd && source environment && cd proj/${{ matrix.proj_name }} &&
        make ${{ matrix.build_params }} PLATFORM=${{ matrix.platform }} TARGET=${{ matrix.target }} -j8 software &&
        make renode-scripts ${{ matrix.build_params }} PLATFORM=${{ matrix.platform }} TARGET=${{ matrix.target }}

    - name: Run tests
      timeout-minutes: 15
      uses: antmicro/renode-test-action@v2.0.0
      with:
        renode-version: '${{ env.RENODE_VERSION }}'
        tests-to-run: proj/${{ matrix.proj_name }}/build/renode/${{ matrix.target }}.robot
        renode-path: '${{ env.RENODE_DIR }}'

    - name: Archive results
      if: ${{ success() || failure() }}
      uses: actions/upload-artifact@v3
      with:
        name: test-results-${{ matrix.proj_name }}-${{ matrix.platform }}-${{ matrix.target }}
        path: |
          report.html
          log.html
          robot_output.xml
          snapshots
