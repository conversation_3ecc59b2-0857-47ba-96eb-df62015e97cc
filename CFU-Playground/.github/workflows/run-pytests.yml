name: run-pytests
on: [push, pull_request]

jobs:
  py-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v3
        with:
          python-version: '3.7'
      - run: bash scripts/setup -ci
      - run: cd proj/avg_pdti8 && pwd && make pytest
      - run: cd proj/example_cfu && pwd && make pytest
      - run: cd proj/hps_accel && pwd && make pytest
      - run: cd proj/mnv2_first && pwd && make pytest
      - run: cd proj/proj_accel_1 && pwd && make pytest
      - run: cd proj/proj_template && pwd && make pytest
      - run: echo "amaranth_cfu tests"; python/amaranth_cfu/run_unit_tests.sh
