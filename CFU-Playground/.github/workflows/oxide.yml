name: oxide_ci
on:
  pull_request:
    paths-ignore:
      - 'docs/**'
      - 'README.md'
      - '**/README.md'
      - 'LICENSE'
      - 'CONTRIBUTING.md'
      - 'CODE_OF_CONDUCT.md'
      - 'AUTHORS'
jobs:
  install-use-oxide-conda:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v3
        with:
          python-version: '3.7'
      - run: bash scripts/setup -ci
      - run: which pip3 && which python3 && which pip
      - run: make env
      - run: which pip3 && which python3 && which pip
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && riscv32-elf-newlib-gcc --version
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && yosys --version && nextpnr-nexus --version
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/proj_template_v && pip3 list && make PLATFORM=hps EXTRA_LITEX_ARGS="--dynamic-clock-control" bitstream
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && ulimit -S -t 900 && ulimit -H -t 900 && cd proj/hps_accel && pip3 list && make PLATFORM=hps EXTRA_LITEX_ARGS="--dynamic-clock-control" bitstream || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && ulimit -S -t 900 && ulimit -H -t 900 && cd proj/hps_accel && pip3 list && make PLATFORM=hps EXTRA_LITEX_ARGS="--cpu-variant=slimopt+cfu --dynamic-clock-control" clean bitstream || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && ulimit -S -t 900 && ulimit -H -t 900 && cd proj/hps_accel && make PLATFORM=hps EXTRA_NEXTPNR_ARGS="--placer-heap-timingweight 52" EXTRA_LITEX_ARGS="--separate-arena --cfu-mport --dynamic-clock-control --cpu-variant=slimopt+cfu --extra-nextpnr-params" clean bitstream || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && ulimit -S -t 240 && ulimit -H -t 240 && cd proj/hps_accel && make PLATFORM=hps EXTRA_NEXTPNR_ARGS="--placer-heap-timingweight 52" EXTRA_LITEX_ARGS="--separate-arena --cfu-mport --dynamic-clock-control --cpu-variant=slimopt+cfu --extra-nextpnr-params --no-compile-software" clean bitstream || true
      - run: pwd && source env/conda/bin/activate cfu-common && source environment && cd proj/mport && pip3 list && make PLATFORM=hps bitstream software
