name: symbiflow
on: 
  pull_request:
    paths-ignore:
      - 'docs/**'
      - 'README.md'
      - '**/README.md'
      - 'LICENSE'
      - 'CONTRIBUTING.md'
      - 'CODE_OF_CONDUCT.md'
      - 'AUTHORS'
jobs:
  install-use-symbiflow:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v3
        with:
          python-version: '3.7'
      - run: bash scripts/setup -ci
      - run: which pip3 && which python3 && which pip
      - run: make install-sf
      - run: echo `pwd`/"env/symbiflow/bin" >> $GITHUB_PATH
      - run: pwd && source env/conda/bin/activate cfu-symbiflow && riscv32-elf-newlib-gcc --version && which python3
      - run: pwd && source env/conda/bin/activate cfu-symbiflow && source environment && export F4PGA_INSTALL_DIR=`pwd`/env/symbiflow && cd proj/proj_template_v && pip3 list && make USE_SYMBIFLOW=1 EXTRA_LITEX_ARGS="--cpu-variant=slimopt+cfu --sys-clk-freq=50000000" clean bitstream
      - run: pwd && source env/conda/bin/activate cfu-symbiflow && source environment && export F4PGA_INSTALL_DIR=`pwd`/env/symbiflow && ulimit -S -t 1200 && ulimit -H -t 1200 &&  cd proj/mnv2_first && pip3 list && make USE_SYMBIFLOW=1 PLATFORM=common_soc TARGET=digilent_arty EXTRA_LITEX_ARGS="--cpu-variant=slimopt+cfu --sys-clk-freq=50000000" clean bitstream || true
