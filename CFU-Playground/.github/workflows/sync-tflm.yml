name: Sync from tflite-micro

on:
  schedule:
    - cron: 0 0 * * *

  # Allow manually triggering of the workflow.
  workflow_dispatch: {}

jobs:
  sync-tflite-micro:
    runs-on: ubuntu-latest

    if: |
      github.repository_owner == 'google' && ( github.event_name == 'workflow_dispatch' || github.event_name == 'schedule' )

    env:
      GH_SERVICE_ACCOUNT_NAME: "CFU-Playground-Bot"
      GH_SERVICE_ACCOUNT_EMAIL: "<EMAIL>"

    steps:
      - name: Clone repository
        uses: actions/checkout@v2
        with:
          ssh-key: ${{ secrets.SSH_DEPLOY_KEY }}

      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.7'

      - name: Setup environment
        run: bash scripts/setup -ci

      - name: Configure git
        run: |
          git config --global user.name $GH_SERVICE_ACCOUNT_NAME
          git config --global user.email $GH_SERVICE_ACCOUNT_EMAIL

      - name: Sync the code, commit and push
        id: sync-and-commit
        run: |
          source environment
          python3 -m pip install -r conf/requirements.txt
          export TFLM_SHA=$(git ls-remote https://github.com/tensorflow/tflite-micro.git main | cut -c1-7)
          ./scripts/sync_from_tflite_micro.sh
          git add *

          if [ -n "$(git status --porcelain)" ]; then
            git commit -sm "Sync from tflite-micro at $TFLM_SHA."
            echo "::set-output name=committed::true"
          else
            echo "::notice title=Early exit::TFLM is up to date."
            echo "::set-output name=committed::false"
          fi

      - name: Create Pull Request
        id: create-pr
        if: steps.sync-and-commit.outputs.committed == 'true'
        uses: peter-evans/create-pull-request@v3
        with:
          branch: sync-from-tflite-micro
          base: main
          delete-branch: true
          signoff: true
          token: ${{ secrets.CFU_BOT_TOKEN }}
          committer: CFU Playground Bot <<EMAIL>>
          author: CFU Playground Bot <<EMAIL>>
          title: (CFU Playground) Automated sync from github.com/tensorflow/tflite-micro
          commit-message: Automated sync from github.com/tensorflow/tflite-micro
          body: "(CFU Playground) Automated sync from github.com/tensorflow/tflite-micro"

      - name: Enable automerge
        if: steps.create-pr.outputs.pull-request-operation == 'created'
        uses: peter-evans/enable-pull-request-automerge@v1
        with:
          token: ${{ secrets.CFU_BOT_TOKEN }}
          merge-method: rebase
          pull-request-number: ${{ steps.create-pr.outputs.pull-request-number }}
